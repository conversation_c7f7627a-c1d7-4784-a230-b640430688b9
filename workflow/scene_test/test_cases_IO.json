[{"test_name": "IO节点-单次输入", "test_type": "chat_user_input", "test_params": {"workspace": "test_workspace", "name": "测试用户输入工作流-单次", "description": "测试需要用户输入的工作流场景", "workflow_file": "workflow/scene_test/user_input_single.json", "messages": [{"role": "user", "content": "请帮我计算一个数字"}], "user_inputs_sequence": [{"UserResponse": "42"}]}, "expected_output": {"finish_reason": "stop", "user_inputs_processed": ["42"], "output_contains": ["数字", "平方", "计算结果"], "validate_user_input_flow": true}}, {"test_name": "IO节点-多次输入", "test_type": "chat_user_input", "test_params": {"workspace": "test_workspace", "name": "测试用户输入工作流-多次", "description": "测试需要多次用户输入的工作流场景", "workflow_file": "workflow/scene_test/user_input_multiple.json", "messages": [{"role": "user", "content": "开始问答流程"}], "user_inputs_sequence": [{"UserResponse": "张三"}, {"UserResponse": "25"}, {"UserResponse": "北京"}]}, "expected_output": {"finish_reason": "stop", "user_inputs_processed": ["张三", "25", "北京"], "validate_user_input_flow": true}}, {"test_name": "IO节点-条件分支", "test_type": "chat_user_input", "test_params": {"workspace": "test_workspace", "name": "测试条件分支用户输入工作流", "description": "测试根据用户输入进行条件分支的工作流", "workflow_file": "workflow/scene_test/user_input_conditional.json", "messages": [{"role": "user", "content": "开始选择流程"}], "user_inputs_sequence": [{"UserResponse": "A"}, {"UserResponse": "选择了选项A的详细信息"}]}, "expected_output": {"finish_reason": "stop", "user_inputs_processed": ["A", "选择了选项A的详细信息"], "validate_user_input_flow": true}}]